/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo h1 {
    color: #764ba2;
    font-size: 1.8rem;
    font-weight: 700;
}

.logo .tagline {
    color: #666;
    font-size: 0.8rem;
    font-weight: 300;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #764ba2;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #f0f0f0;
    margin-bottom: 1rem;
    font-weight: 600;
}

.hero-description {
    font-size: 1.1rem;
    color: #e0e0e0;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.btn-primary {
    background: #ff6b6b;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-primary:hover {
    background: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Stream Preview */
.stream-preview {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stream-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: white;
    font-weight: 600;
}

.stream-status.offline i {
    color: #ff6b6b;
}

.follower-count {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #e0e0e0;
    font-size: 1.1rem;
}

/* Section Styles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.about {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.about-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.about-card:hover {
    transform: translateY(-5px);
}

.about-card i {
    font-size: 3rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.about-card h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.about-card p {
    color: #e0e0e0;
    line-height: 1.6;
}

/* Stream Section */
.stream-section {
    padding: 80px 0;
}

.stream-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stream-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stream-card h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.stream-card p {
    color: #e0e0e0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.promo-section {
    text-align: center;
}

.promo-section h3 {
    color: white;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.promo-card {
    background: linear-gradient(135deg, #ff6b6b, #ff8e53);
    padding: 2rem;
    border-radius: 15px;
    color: white;
    max-width: 400px;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.promo-card i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.promo-card h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.copy-code-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.copy-code-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-card i {
    font-size: 2.5rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.contact-card h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.contact-card p {
    color: #e0e0e0;
    margin-bottom: 1.5rem;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    padding: 40px 0 20px;
    color: white;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ff6b6b;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #e0e0e0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ff6b6b;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(255, 255, 255, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .about-grid,
    .stream-info,
    .contact-info {
        grid-template-columns: 1fr;
    }
    
    .hero-buttons {
        justify-content: center;
    }
}
